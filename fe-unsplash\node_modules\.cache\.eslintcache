[{"C:\\xampp\\htdocs\\dashboard\\JamesDy\\ngnair\\training\\unspash\\fe-unsplash\\src\\index.js": "1", "C:\\xampp\\htdocs\\dashboard\\JamesDy\\ngnair\\training\\unspash\\fe-unsplash\\src\\App.js": "2", "C:\\xampp\\htdocs\\dashboard\\JamesDy\\ngnair\\training\\unspash\\fe-unsplash\\src\\reportWebVitals.js": "3"}, {"size": 535, "mtime": 1756249895000, "results": "4", "hashOfConfig": "5"}, {"size": 7768, "mtime": 1756448882185, "results": "6", "hashOfConfig": "5"}, {"size": 362, "mtime": 1756249896000, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "119bwxy", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\xampp\\htdocs\\dashboard\\JamesDy\\ngnair\\training\\unspash\\fe-unsplash\\src\\index.js", [], [], "C:\\xampp\\htdocs\\dashboard\\JamesDy\\ngnair\\training\\unspash\\fe-unsplash\\src\\App.js", ["17"], [], "C:\\xampp\\htdocs\\dashboard\\JamesDy\\ngnair\\training\\unspash\\fe-unsplash\\src\\reportWebVitals.js", [], [], {"ruleId": "18", "severity": 1, "message": "19", "line": 76, "column": 6, "nodeType": "20", "endLine": 76, "endColumn": 8, "suggestions": "21"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchPhotos'. Either include it or remove the dependency array.", "ArrayExpression", ["22"], {"desc": "23", "fix": "24"}, "Update the dependencies array to be: [fetchPhotos]", {"range": "25", "text": "26"}, [2112, 2114], "[fetchPhotos]"]