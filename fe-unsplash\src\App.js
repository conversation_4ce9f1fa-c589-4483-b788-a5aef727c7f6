
import React, { useEffect, useState } from 'react';
import axios from 'axios';
import './App.css';

export default function App() {
  const [photos, setPhotos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [query, setQuery] = useState('');
  const [selectedPhoto, setSelectedPhoto] = useState(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const fetchPhotos = async (pageNum = 1, searchQuery = '') => {
    try {
      setLoading(true);
      setError(null);

      let response;
      const baseURL = process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:3000';

      if (searchQuery) {
        response = await axios.get(`${baseURL}/search/photos`, {
          params: { query: searchQuery, page: pageNum, per_page: 20 }
        });
        const newPhotos = pageNum === 1 ? response.data.results : [...photos, ...response.data.results];
        setPhotos(newPhotos);
        setHasMore(response.data.results.length === 20);
      } else {
        response = await axios.get(`${baseURL}/photos`, {
          params: { page: pageNum, per_page: 20 }
        });
        const newPhotos = pageNum === 1 ? response.data : [...photos, ...response.data];
        setPhotos(newPhotos);
        setHasMore(response.data.length === 20);
      }
    } catch (error) {
      console.error('Error fetching photos:', error);
      setError('Failed to load photos. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (e) => {
    e.preventDefault();
    if (query.trim()) {
      setPage(1);
      await fetchPhotos(1, query.trim());
    }
  };

  const handleClearSearch = () => {
    setQuery('');
    setPage(1);
    fetchPhotos(1);
  };

  const loadMore = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    fetchPhotos(nextPage, query);
  };

  const openModal = (photo) => {
    setSelectedPhoto(photo);
  };

  const closeModal = () => {
    setSelectedPhoto(null);
  };

  useEffect(() => {
    fetchPhotos();
  }, []);

  return (
    <div className="app">
      {/* Header */}
      <header className="header">
        <div className="container">
          <h1 className="title">
            <span className="title-icon">📸</span>
            Unsplash Gallery
          </h1>
          <p className="subtitle">Discover beautiful photography from around the world</p>
        </div>
      </header>

      {/* Search Section */}
      <section className="search-section">
        <div className="container">
          <form onSubmit={handleSearch} className="search-form">
            <div className="search-input-group">
              <input
                type="text"
                placeholder="Search for photos..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="search-input"
              />
              <button type="submit" className="search-button">
                🔍 Search
              </button>
              {query && (
                <button type="button" onClick={handleClearSearch} className="clear-button">
                  ✕ Clear
                </button>
              )}
            </div>
          </form>
        </div>
      </section>

      {/* Main Content */}
      <main className="main-content">
        <div className="container">
          {error && (
            <div className="error-message">
              <p>{error}</p>
              <button onClick={() => fetchPhotos(1, query)} className="retry-button">
                Try Again
              </button>
            </div>
          )}

          {loading && page === 1 ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>Loading amazing photos...</p>
            </div>
          ) : (
            <>
              <div className="photos-grid">
                {photos.map((photo) => (
                  <div key={photo.id} className="photo-card" onClick={() => openModal(photo)}>
                    <div className="photo-image-container">
                      <img
                        src={photo.urls.small}
                        alt={photo.alt_description || 'Unsplash photo'}
                        className="photo-image"
                        loading="lazy"
                      />
                      <div className="photo-overlay">
                        <div className="photo-info">
                          <p className="photo-author">📷 {photo.user.name}</p>
                          <p className="photo-likes">❤️ {photo.likes}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {photos.length === 0 && !loading && (
                <div className="no-results">
                  <p>No photos found. Try a different search term.</p>
                </div>
              )}

              {hasMore && photos.length > 0 && (
                <div className="load-more-container">
                  <button
                    onClick={loadMore}
                    disabled={loading}
                    className="load-more-button"
                  >
                    {loading ? 'Loading...' : 'Load More Photos'}
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </main>

      {/* Photo Modal */}
      {selectedPhoto && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <button className="modal-close" onClick={closeModal}>✕</button>
            <div className="modal-image-container">
              <img
                src={selectedPhoto.urls.regular}
                alt={selectedPhoto.alt_description || 'Unsplash photo'}
                className="modal-image"
              />
            </div>
            <div className="modal-info">
              <h3 className="modal-title">{selectedPhoto.alt_description || 'Untitled'}</h3>
              <div className="modal-details">
                <div className="modal-author">
                  <img
                    src={selectedPhoto.user.profile_image.small}
                    alt={selectedPhoto.user.name}
                    className="author-avatar"
                  />
                  <div>
                    <p className="author-name">{selectedPhoto.user.name}</p>
                    <p className="author-username">@{selectedPhoto.user.username}</p>
                  </div>
                </div>
                <div className="modal-stats">
                  <span className="stat">❤️ {selectedPhoto.likes}</span>
                  <span className="stat">📥 {selectedPhoto.downloads || 'N/A'}</span>
                  <span className="stat">👁️ {selectedPhoto.views || 'N/A'}</span>
                </div>
              </div>
              <div className="modal-actions">
                <a
                  href={selectedPhoto.links.download}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="download-button"
                >
                  Download Photo
                </a>
                <a
                  href={selectedPhoto.user.links.html}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="profile-button"
                >
                  View Profile
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
