Sandbox Integration Exercise – Photo App
API docs: https://unsplash.com/documentation

Access Key: ZyY9kysQ-Nxn-SP7bzuEADa5NBEmU4Qsw_0wkSia_jY
Secret Key: Is81UR3GQulyfOTVleEC-7OGLb3-XkSM6uDLFX2rtK0
Application ID: 795094
Deadline 2:40pm to email to <PERSON> <EMAIL>
Exercise Instructions
Pre-Requirements
[ ] Sandbox credentials for unsplash
[ ] ChatGPT access
[ ] Postman installed and ready
[ ] CoPilot in IDE
[ ] Repo Zip File
[ ] Timer set for 30 minutes

Objectives: Build a Photo App that can get photos, search, list
Research Unsplash Guide
Import the App Code into IDE
Finish the code build for Photo App with Docker
Accepted Frameworks
NestJS and NextJS/ReactJS
Ruby on Rails (fullstack)
Integrate the frontend to backend 
Run both apps in Docker and record a demo of the features.

** extra points for style and functionality 