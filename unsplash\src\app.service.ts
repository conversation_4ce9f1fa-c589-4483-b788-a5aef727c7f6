import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

@Injectable()
export class AppService {
  private readonly unsplashApiUrl = 'https://api.unsplash.com';
  private readonly accessKey: string;

  constructor(private configService: ConfigService) {
    this.accessKey = 'ZyY9kysQ-Nxn-SP7bzuEADa5NBEmU4Qsw_0wkSia_jY';
  }

  getHello(): string {
    return 'Unsplash Photo App API';
  }

  async getPhotos(page: number = 1, perPage: number = 30) {
    try {
      const response = await axios.get(`${this.unsplashApiUrl}/photos`, {
        params: {
          page,
          per_page: perPage,
        },
        headers: {
          Authorization: `Client-ID ${this.accessKey}`,
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to fetch photos: ${error.message}`);
    }
  }

  async searchPhotos(query: string, page: number = 1, perPage: number = 30) {
    try {
      const response = await axios.get(`${this.unsplashApiUrl}/search/photos`, {
        params: {
          query,
          page,
          per_page: perPage,
        },
        headers: {
          Authorization: `Client-ID ${this.accessKey}`,
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to search photos: ${error.message}`);
    }
  }

  async getPhotoById(id: string) {
    try {
      const response = await axios.get(`${this.unsplashApiUrl}/photos/${id}`, {
        headers: {
          Authorization: `Client-ID ${this.accessKey}`,
        },
      });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to fetch photo: ${error.message}`);
    }
  }
}
