"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = __importDefault(require("axios"));
let AppService = class AppService {
    configService;
    unsplashApiUrl = 'https://api.unsplash.com';
    accessKey;
    constructor(configService) {
        this.configService = configService;
        this.accessKey = 'ZyY9kysQ-Nxn-SP7bzuEADa5NBEmU4Qsw_0wkSia_jY';
    }
    getHello() {
        return 'Unsplash Photo App API';
    }
    async getPhotos(page = 1, perPage = 30) {
        try {
            const response = await axios_1.default.get(`${this.unsplashApiUrl}/photos`, {
                params: {
                    page,
                    per_page: perPage,
                },
                headers: {
                    Authorization: `Client-ID ${this.accessKey}`,
                },
            });
            return response.data;
        }
        catch (error) {
            throw new Error(`Failed to fetch photos: ${error.message}`);
        }
    }
    async searchPhotos(query, page = 1, perPage = 30) {
        try {
            const response = await axios_1.default.get(`${this.unsplashApiUrl}/search/photos`, {
                params: {
                    query,
                    page,
                    per_page: perPage,
                },
                headers: {
                    Authorization: `Client-ID ${this.accessKey}`,
                },
            });
            return response.data;
        }
        catch (error) {
            throw new Error(`Failed to search photos: ${error.message}`);
        }
    }
    async getPhotoById(id) {
        try {
            const response = await axios_1.default.get(`${this.unsplashApiUrl}/photos/${id}`, {
                headers: {
                    Authorization: `Client-ID ${this.accessKey}`,
                },
            });
            return response.data;
        }
        catch (error) {
            throw new Error(`Failed to fetch photo: ${error.message}`);
        }
    }
};
exports.AppService = AppService;
exports.AppService = AppService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], AppService);
//# sourceMappingURL=app.service.js.map