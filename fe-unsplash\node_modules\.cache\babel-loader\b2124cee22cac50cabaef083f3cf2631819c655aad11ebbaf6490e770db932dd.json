{"ast": null, "code": "var _jsxFileName = \"C:\\\\xampp\\\\htdocs\\\\dashboard\\\\JamesDy\\\\ngnair\\\\training\\\\unspash\\\\fe-unsplash\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport './App.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function App() {\n  _s();\n  const [photos, setPhotos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [query, setQuery] = useState('');\n  const [selectedPhoto, setSelectedPhoto] = useState(null);\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n  const fetchPhotos = async (pageNum = 1, searchQuery = '') => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      const baseURL = process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:3000';\n      if (searchQuery) {\n        response = await axios.get(`${baseURL}/search/photos`, {\n          params: {\n            query: searchQuery,\n            page: pageNum,\n            per_page: 20\n          }\n        });\n        const newPhotos = pageNum === 1 ? response.data.results : [...photos, ...response.data.results];\n        setPhotos(newPhotos);\n        setHasMore(response.data.results.length === 20);\n      } else {\n        response = await axios.get(`${baseURL}/photos`, {\n          params: {\n            page: pageNum,\n            per_page: 20\n          }\n        });\n        const newPhotos = pageNum === 1 ? response.data : [...photos, ...response.data];\n        setPhotos(newPhotos);\n        setHasMore(response.data.length === 20);\n      }\n    } catch (error) {\n      console.error('Error fetching photos:', error);\n      setError('Failed to load photos. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSearch = async e => {\n    e.preventDefault();\n    if (query.trim()) {\n      setPage(1);\n      await fetchPhotos(1, query.trim());\n    }\n  };\n  const handleClearSearch = () => {\n    setQuery('');\n    setPage(1);\n    fetchPhotos(1);\n  };\n  const loadMore = () => {\n    const nextPage = page + 1;\n    setPage(nextPage);\n    fetchPhotos(nextPage, query);\n  };\n  const openModal = photo => {\n    setSelectedPhoto(photo);\n  };\n  const closeModal = () => {\n    setSelectedPhoto(null);\n  };\n  useEffect(() => {\n    fetchPhotos();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"title\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"title-icon\",\n            children: \"\\uD83D\\uDCF8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), \"Unsplash Gallery\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"subtitle\",\n          children: \"Discover beautiful photography from around the world\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"search-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSearch,\n          className: \"search-form\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"search-input-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search for photos...\",\n              value: query,\n              onChange: e => setQuery(e.target.value),\n              className: \"search-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"search-button\",\n              children: \"\\uD83D\\uDD0D Search\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), query && /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: handleClearSearch,\n              className: \"clear-button\",\n              children: \"\\u2715 Clear\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"main-content\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => fetchPhotos(1, query),\n            className: \"retry-button\",\n            children: \"Try Again\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this), loading && page === 1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading amazing photos...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"photos-grid\",\n            children: photos.map(photo => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"photo-card\",\n              onClick: () => openModal(photo),\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"photo-image-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: photo.urls.small,\n                  alt: photo.alt_description || 'Unsplash photo',\n                  className: \"photo-image\",\n                  loading: \"lazy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"photo-overlay\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"photo-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"photo-author\",\n                      children: [\"\\uD83D\\uDCF7 \", photo.user.name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"photo-likes\",\n                      children: [\"\\u2764\\uFE0F \", photo.likes]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 148,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 21\n              }, this)\n            }, photo.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this), photos.length === 0 && !loading && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-results\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No photos found. Try a different search term.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 17\n          }, this), hasMore && photos.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"load-more-container\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: loadMore,\n              disabled: loading,\n              className: \"load-more-button\",\n              children: loading ? 'Loading...' : 'Load More Photos'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), selectedPhoto && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: closeModal,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"modal-close\",\n          onClick: closeModal,\n          children: \"\\u2715\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-image-container\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: selectedPhoto.urls.regular,\n            alt: selectedPhoto.alt_description || 'Unsplash photo',\n            className: \"modal-image\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"modal-title\",\n            children: selectedPhoto.alt_description || 'Untitled'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-author\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: selectedPhoto.user.profile_image.small,\n                alt: selectedPhoto.user.name,\n                className: \"author-avatar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"author-name\",\n                  children: selectedPhoto.user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"author-username\",\n                  children: [\"@\", selectedPhoto.user.username]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-stats\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat\",\n                children: [\"\\u2764\\uFE0F \", selectedPhoto.likes]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat\",\n                children: [\"\\uD83D\\uDCE5 \", selectedPhoto.downloads || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"stat\",\n                children: [\"\\uD83D\\uDC41\\uFE0F \", selectedPhoto.views || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-actions\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: selectedPhoto.links.download,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"download-button\",\n              children: \"Download Photo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: selectedPhoto.user.links.html,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              className: \"profile-button\",\n              children: \"View Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"pTDjX/GCjslStdyNhbZWLdq0q7I=\");\n_c = App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "_s", "photos", "setPhotos", "loading", "setLoading", "error", "setError", "query", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPhoto", "page", "setPage", "hasMore", "setHasMore", "fetchPhotos", "pageNum", "searchQuery", "response", "baseURL", "process", "env", "NODE_ENV", "get", "params", "per_page", "newPhotos", "data", "results", "length", "console", "handleSearch", "e", "preventDefault", "trim", "handleClearSearch", "loadMore", "nextPage", "openModal", "photo", "closeModal", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "placeholder", "value", "onChange", "target", "onClick", "map", "src", "urls", "small", "alt", "alt_description", "user", "name", "likes", "id", "disabled", "stopPropagation", "regular", "profile_image", "username", "downloads", "views", "href", "links", "download", "rel", "html", "_c", "$RefreshReg$"], "sources": ["C:/xampp/htdocs/dashboard/JamesDy/ngnair/training/unspash/fe-unsplash/src/App.js"], "sourcesContent": ["\nimport React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport './App.css';\n\nexport default function App() {\n  const [photos, setPhotos] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [query, setQuery] = useState('');\n  const [selectedPhoto, setSelectedPhoto] = useState(null);\n  const [page, setPage] = useState(1);\n  const [hasMore, setHasMore] = useState(true);\n\n  const fetchPhotos = async (pageNum = 1, searchQuery = '') => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n      const baseURL = process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:3000';\n\n      if (searchQuery) {\n        response = await axios.get(`${baseURL}/search/photos`, {\n          params: { query: searchQuery, page: pageNum, per_page: 20 }\n        });\n        const newPhotos = pageNum === 1 ? response.data.results : [...photos, ...response.data.results];\n        setPhotos(newPhotos);\n        setHasMore(response.data.results.length === 20);\n      } else {\n        response = await axios.get(`${baseURL}/photos`, {\n          params: { page: pageNum, per_page: 20 }\n        });\n        const newPhotos = pageNum === 1 ? response.data : [...photos, ...response.data];\n        setPhotos(newPhotos);\n        setHasMore(response.data.length === 20);\n      }\n    } catch (error) {\n      console.error('Error fetching photos:', error);\n      setError('Failed to load photos. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSearch = async (e) => {\n    e.preventDefault();\n    if (query.trim()) {\n      setPage(1);\n      await fetchPhotos(1, query.trim());\n    }\n  };\n\n  const handleClearSearch = () => {\n    setQuery('');\n    setPage(1);\n    fetchPhotos(1);\n  };\n\n  const loadMore = () => {\n    const nextPage = page + 1;\n    setPage(nextPage);\n    fetchPhotos(nextPage, query);\n  };\n\n  const openModal = (photo) => {\n    setSelectedPhoto(photo);\n  };\n\n  const closeModal = () => {\n    setSelectedPhoto(null);\n  };\n\n  useEffect(() => {\n    fetchPhotos();\n  }, []);\n\n  return (\n    <div className=\"app\">\n      {/* Header */}\n      <header className=\"header\">\n        <div className=\"container\">\n          <h1 className=\"title\">\n            <span className=\"title-icon\">📸</span>\n            Unsplash Gallery\n          </h1>\n          <p className=\"subtitle\">Discover beautiful photography from around the world</p>\n        </div>\n      </header>\n\n      {/* Search Section */}\n      <section className=\"search-section\">\n        <div className=\"container\">\n          <form onSubmit={handleSearch} className=\"search-form\">\n            <div className=\"search-input-group\">\n              <input\n                type=\"text\"\n                placeholder=\"Search for photos...\"\n                value={query}\n                onChange={(e) => setQuery(e.target.value)}\n                className=\"search-input\"\n              />\n              <button type=\"submit\" className=\"search-button\">\n                🔍 Search\n              </button>\n              {query && (\n                <button type=\"button\" onClick={handleClearSearch} className=\"clear-button\">\n                  ✕ Clear\n                </button>\n              )}\n            </div>\n          </form>\n        </div>\n      </section>\n\n      {/* Main Content */}\n      <main className=\"main-content\">\n        <div className=\"container\">\n          {error && (\n            <div className=\"error-message\">\n              <p>{error}</p>\n              <button onClick={() => fetchPhotos(1, query)} className=\"retry-button\">\n                Try Again\n              </button>\n            </div>\n          )}\n\n          {loading && page === 1 ? (\n            <div className=\"loading-container\">\n              <div className=\"loading-spinner\"></div>\n              <p>Loading amazing photos...</p>\n            </div>\n          ) : (\n            <>\n              <div className=\"photos-grid\">\n                {photos.map((photo) => (\n                  <div key={photo.id} className=\"photo-card\" onClick={() => openModal(photo)}>\n                    <div className=\"photo-image-container\">\n                      <img\n                        src={photo.urls.small}\n                        alt={photo.alt_description || 'Unsplash photo'}\n                        className=\"photo-image\"\n                        loading=\"lazy\"\n                      />\n                      <div className=\"photo-overlay\">\n                        <div className=\"photo-info\">\n                          <p className=\"photo-author\">📷 {photo.user.name}</p>\n                          <p className=\"photo-likes\">❤️ {photo.likes}</p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {photos.length === 0 && !loading && (\n                <div className=\"no-results\">\n                  <p>No photos found. Try a different search term.</p>\n                </div>\n              )}\n\n              {hasMore && photos.length > 0 && (\n                <div className=\"load-more-container\">\n                  <button\n                    onClick={loadMore}\n                    disabled={loading}\n                    className=\"load-more-button\"\n                  >\n                    {loading ? 'Loading...' : 'Load More Photos'}\n                  </button>\n                </div>\n              )}\n            </>\n          )}\n        </div>\n      </main>\n\n      {/* Photo Modal */}\n      {selectedPhoto && (\n        <div className=\"modal-overlay\" onClick={closeModal}>\n          <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n            <button className=\"modal-close\" onClick={closeModal}>✕</button>\n            <div className=\"modal-image-container\">\n              <img\n                src={selectedPhoto.urls.regular}\n                alt={selectedPhoto.alt_description || 'Unsplash photo'}\n                className=\"modal-image\"\n              />\n            </div>\n            <div className=\"modal-info\">\n              <h3 className=\"modal-title\">{selectedPhoto.alt_description || 'Untitled'}</h3>\n              <div className=\"modal-details\">\n                <div className=\"modal-author\">\n                  <img\n                    src={selectedPhoto.user.profile_image.small}\n                    alt={selectedPhoto.user.name}\n                    className=\"author-avatar\"\n                  />\n                  <div>\n                    <p className=\"author-name\">{selectedPhoto.user.name}</p>\n                    <p className=\"author-username\">@{selectedPhoto.user.username}</p>\n                  </div>\n                </div>\n                <div className=\"modal-stats\">\n                  <span className=\"stat\">❤️ {selectedPhoto.likes}</span>\n                  <span className=\"stat\">📥 {selectedPhoto.downloads || 'N/A'}</span>\n                  <span className=\"stat\">👁️ {selectedPhoto.views || 'N/A'}</span>\n                </div>\n              </div>\n              <div className=\"modal-actions\">\n                <a\n                  href={selectedPhoto.links.download}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"download-button\"\n                >\n                  Download Photo\n                </a>\n                <a\n                  href={selectedPhoto.user.links.html}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"profile-button\"\n                >\n                  View Profile\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "mappings": ";;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,eAAe,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkB,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAE5C,MAAMsB,WAAW,GAAG,MAAAA,CAAOC,OAAO,GAAG,CAAC,EAAEC,WAAW,GAAG,EAAE,KAAK;IAC3D,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAIY,QAAQ;MACZ,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,MAAM,GAAG,uBAAuB;MAExF,IAAIL,WAAW,EAAE;QACfC,QAAQ,GAAG,MAAMxB,KAAK,CAAC6B,GAAG,CAAC,GAAGJ,OAAO,gBAAgB,EAAE;UACrDK,MAAM,EAAE;YAAEjB,KAAK,EAAEU,WAAW;YAAEN,IAAI,EAAEK,OAAO;YAAES,QAAQ,EAAE;UAAG;QAC5D,CAAC,CAAC;QACF,MAAMC,SAAS,GAAGV,OAAO,KAAK,CAAC,GAAGE,QAAQ,CAACS,IAAI,CAACC,OAAO,GAAG,CAAC,GAAG3B,MAAM,EAAE,GAAGiB,QAAQ,CAACS,IAAI,CAACC,OAAO,CAAC;QAC/F1B,SAAS,CAACwB,SAAS,CAAC;QACpBZ,UAAU,CAACI,QAAQ,CAACS,IAAI,CAACC,OAAO,CAACC,MAAM,KAAK,EAAE,CAAC;MACjD,CAAC,MAAM;QACLX,QAAQ,GAAG,MAAMxB,KAAK,CAAC6B,GAAG,CAAC,GAAGJ,OAAO,SAAS,EAAE;UAC9CK,MAAM,EAAE;YAAEb,IAAI,EAAEK,OAAO;YAAES,QAAQ,EAAE;UAAG;QACxC,CAAC,CAAC;QACF,MAAMC,SAAS,GAAGV,OAAO,KAAK,CAAC,GAAGE,QAAQ,CAACS,IAAI,GAAG,CAAC,GAAG1B,MAAM,EAAE,GAAGiB,QAAQ,CAACS,IAAI,CAAC;QAC/EzB,SAAS,CAACwB,SAAS,CAAC;QACpBZ,UAAU,CAACI,QAAQ,CAACS,IAAI,CAACE,MAAM,KAAK,EAAE,CAAC;MACzC;IACF,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CC,QAAQ,CAAC,0CAA0C,CAAC;IACtD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2B,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI1B,KAAK,CAAC2B,IAAI,CAAC,CAAC,EAAE;MAChBtB,OAAO,CAAC,CAAC,CAAC;MACV,MAAMG,WAAW,CAAC,CAAC,EAAER,KAAK,CAAC2B,IAAI,CAAC,CAAC,CAAC;IACpC;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B3B,QAAQ,CAAC,EAAE,CAAC;IACZI,OAAO,CAAC,CAAC,CAAC;IACVG,WAAW,CAAC,CAAC,CAAC;EAChB,CAAC;EAED,MAAMqB,QAAQ,GAAGA,CAAA,KAAM;IACrB,MAAMC,QAAQ,GAAG1B,IAAI,GAAG,CAAC;IACzBC,OAAO,CAACyB,QAAQ,CAAC;IACjBtB,WAAW,CAACsB,QAAQ,EAAE9B,KAAK,CAAC;EAC9B,CAAC;EAED,MAAM+B,SAAS,GAAIC,KAAK,IAAK;IAC3B7B,gBAAgB,CAAC6B,KAAK,CAAC;EACzB,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB9B,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAEDlB,SAAS,CAAC,MAAM;IACduB,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEnB,OAAA;IAAK6C,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAElB9C,OAAA;MAAQ6C,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxB9C,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB9C,OAAA;UAAI6C,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACnB9C,OAAA;YAAM6C,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,oBAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlD,OAAA;UAAG6C,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAoD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTlD,OAAA;MAAS6C,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eACjC9C,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB9C,OAAA;UAAMmD,QAAQ,EAAEhB,YAAa;UAACU,SAAS,EAAC,aAAa;UAAAC,QAAA,eACnD9C,OAAA;YAAK6C,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC9C,OAAA;cACEoD,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,sBAAsB;cAClCC,KAAK,EAAE3C,KAAM;cACb4C,QAAQ,EAAGnB,CAAC,IAAKxB,QAAQ,CAACwB,CAAC,CAACoB,MAAM,CAACF,KAAK,CAAE;cAC1CT,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACFlD,OAAA;cAAQoD,IAAI,EAAC,QAAQ;cAACP,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAEhD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACRvC,KAAK,iBACJX,OAAA;cAAQoD,IAAI,EAAC,QAAQ;cAACK,OAAO,EAAElB,iBAAkB;cAACM,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAE3E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVlD,OAAA;MAAM6C,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC5B9C,OAAA;QAAK6C,SAAS,EAAC,WAAW;QAAAC,QAAA,GACvBrC,KAAK,iBACJT,OAAA;UAAK6C,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B9C,OAAA;YAAA8C,QAAA,EAAIrC;UAAK;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACdlD,OAAA;YAAQyD,OAAO,EAAEA,CAAA,KAAMtC,WAAW,CAAC,CAAC,EAAER,KAAK,CAAE;YAACkC,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAEA3C,OAAO,IAAIQ,IAAI,KAAK,CAAC,gBACpBf,OAAA;UAAK6C,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9C,OAAA;YAAK6C,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvClD,OAAA;YAAA8C,QAAA,EAAG;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,gBAENlD,OAAA,CAAAE,SAAA;UAAA4C,QAAA,gBACE9C,OAAA;YAAK6C,SAAS,EAAC,aAAa;YAAAC,QAAA,EACzBzC,MAAM,CAACqD,GAAG,CAAEf,KAAK,iBAChB3C,OAAA;cAAoB6C,SAAS,EAAC,YAAY;cAACY,OAAO,EAAEA,CAAA,KAAMf,SAAS,CAACC,KAAK,CAAE;cAAAG,QAAA,eACzE9C,OAAA;gBAAK6C,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpC9C,OAAA;kBACE2D,GAAG,EAAEhB,KAAK,CAACiB,IAAI,CAACC,KAAM;kBACtBC,GAAG,EAAEnB,KAAK,CAACoB,eAAe,IAAI,gBAAiB;kBAC/ClB,SAAS,EAAC,aAAa;kBACvBtC,OAAO,EAAC;gBAAM;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACFlD,OAAA;kBAAK6C,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5B9C,OAAA;oBAAK6C,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzB9C,OAAA;sBAAG6C,SAAS,EAAC,cAAc;sBAAAC,QAAA,GAAC,eAAG,EAACH,KAAK,CAACqB,IAAI,CAACC,IAAI;oBAAA;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpDlD,OAAA;sBAAG6C,SAAS,EAAC,aAAa;sBAAAC,QAAA,GAAC,eAAG,EAACH,KAAK,CAACuB,KAAK;oBAAA;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAdEP,KAAK,CAACwB,EAAE;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeb,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAEL7C,MAAM,CAAC4B,MAAM,KAAK,CAAC,IAAI,CAAC1B,OAAO,iBAC9BP,OAAA;YAAK6C,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzB9C,OAAA;cAAA8C,QAAA,EAAG;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CACN,EAEAjC,OAAO,IAAIZ,MAAM,CAAC4B,MAAM,GAAG,CAAC,iBAC3BjC,OAAA;YAAK6C,SAAS,EAAC,qBAAqB;YAAAC,QAAA,eAClC9C,OAAA;cACEyD,OAAO,EAAEjB,QAAS;cAClB4B,QAAQ,EAAE7D,OAAQ;cAClBsC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAE3BvC,OAAO,GAAG,YAAY,GAAG;YAAkB;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA,eACD,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGNrC,aAAa,iBACZb,OAAA;MAAK6C,SAAS,EAAC,eAAe;MAACY,OAAO,EAAEb,UAAW;MAAAE,QAAA,eACjD9C,OAAA;QAAK6C,SAAS,EAAC,eAAe;QAACY,OAAO,EAAGrB,CAAC,IAAKA,CAAC,CAACiC,eAAe,CAAC,CAAE;QAAAvB,QAAA,gBACjE9C,OAAA;UAAQ6C,SAAS,EAAC,aAAa;UAACY,OAAO,EAAEb,UAAW;UAAAE,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC/DlD,OAAA;UAAK6C,SAAS,EAAC,uBAAuB;UAAAC,QAAA,eACpC9C,OAAA;YACE2D,GAAG,EAAE9C,aAAa,CAAC+C,IAAI,CAACU,OAAQ;YAChCR,GAAG,EAAEjD,aAAa,CAACkD,eAAe,IAAI,gBAAiB;YACvDlB,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNlD,OAAA;UAAK6C,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9C,OAAA;YAAI6C,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEjC,aAAa,CAACkD,eAAe,IAAI;UAAU;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9ElD,OAAA;YAAK6C,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B9C,OAAA;cAAK6C,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B9C,OAAA;gBACE2D,GAAG,EAAE9C,aAAa,CAACmD,IAAI,CAACO,aAAa,CAACV,KAAM;gBAC5CC,GAAG,EAAEjD,aAAa,CAACmD,IAAI,CAACC,IAAK;gBAC7BpB,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACFlD,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAG6C,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEjC,aAAa,CAACmD,IAAI,CAACC;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxDlD,OAAA;kBAAG6C,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,GAAC,GAAC,EAACjC,aAAa,CAACmD,IAAI,CAACQ,QAAQ;gBAAA;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlD,OAAA;cAAK6C,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B9C,OAAA;gBAAM6C,SAAS,EAAC,MAAM;gBAAAC,QAAA,GAAC,eAAG,EAACjC,aAAa,CAACqD,KAAK;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACtDlD,OAAA;gBAAM6C,SAAS,EAAC,MAAM;gBAAAC,QAAA,GAAC,eAAG,EAACjC,aAAa,CAAC4D,SAAS,IAAI,KAAK;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnElD,OAAA;gBAAM6C,SAAS,EAAC,MAAM;gBAAAC,QAAA,GAAC,qBAAI,EAACjC,aAAa,CAAC6D,KAAK,IAAI,KAAK;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlD,OAAA;YAAK6C,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B9C,OAAA;cACE2E,IAAI,EAAE9D,aAAa,CAAC+D,KAAK,CAACC,QAAS;cACnCrB,MAAM,EAAC,QAAQ;cACfsB,GAAG,EAAC,qBAAqB;cACzBjC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlD,OAAA;cACE2E,IAAI,EAAE9D,aAAa,CAACmD,IAAI,CAACY,KAAK,CAACG,IAAK;cACpCvB,MAAM,EAAC,QAAQ;cACfsB,GAAG,EAAC,qBAAqB;cACzBjC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC3B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC9C,EAAA,CApOuBD,GAAG;AAAA6E,EAAA,GAAH7E,GAAG;AAAA,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}