import { Controller, Get, Query, Param } from '@nestjs/common';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('photos')
  async getPhotos(
    @Query('page') page: string = '1',
    @Query('per_page') perPage: string = '30',
  ) {
    return this.appService.getPhotos(parseInt(page), parseInt(perPage));
  }

  @Get('search/photos')
  async searchPhotos(
    @Query('query') query: string,
    @Query('page') page: string = '1',
    @Query('per_page') perPage: string = '30',
  ) {
    if (!query) {
      throw new Error('Query parameter is required');
    }
    return this.appService.searchPhotos(query, parseInt(page), parseInt(perPage));
  }

  @Get('photos/:id')
  async getPhotoById(@Param('id') id: string) {
    return this.appService.getPhotoById(id);
  }
}
