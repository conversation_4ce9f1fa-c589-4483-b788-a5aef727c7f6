version: '3.8'

services:
  backend:
    build:
      context: ./unsplash
      dockerfile: Dockerfile
    container_name: unsplash-backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - UNSPLASH_ACCESS_KEY=ZyY9kysQ-Nxn-SP7bzuEADa5NBEmU4Qsw_0wkSia_jY
    networks:
      - unsplash-network
    restart: unless-stopped

  frontend:
    build:
      context: ./fe-unsplash
      dockerfile: Dockerfile
    container_name: unsplash-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - unsplash-network
    restart: unless-stopped

networks:
  unsplash-network:
    driver: bridge

volumes:
  node_modules_backend:
  node_modules_frontend:
