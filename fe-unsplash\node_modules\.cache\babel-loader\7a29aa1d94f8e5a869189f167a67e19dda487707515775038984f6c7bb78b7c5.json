{"ast": null, "code": "'use strict';\n\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toObject = require('../internals/to-object');\nvar hasOwnProperty = uncurryThis({}.hasOwnProperty);\n\n// `HasOwnProperty` abstract operation\n// https://tc39.es/ecma262/#sec-hasownproperty\n// eslint-disable-next-line es/no-object-hasown -- safe\nmodule.exports = Object.hasOwn || function hasOwn(it, key) {\n  return hasOwnProperty(toObject(it), key);\n};", "map": {"version": 3, "names": ["uncurryThis", "require", "toObject", "hasOwnProperty", "module", "exports", "Object", "hasOwn", "it", "key"], "sources": ["C:/xampp/htdocs/dashboard/JamesDy/ngnair/training/unspash/fe-unsplash/node_modules/core-js-pure/internals/has-own-property.js"], "sourcesContent": ["'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toObject = require('../internals/to-object');\n\nvar hasOwnProperty = uncurryThis({}.hasOwnProperty);\n\n// `HasOwnProperty` abstract operation\n// https://tc39.es/ecma262/#sec-hasownproperty\n// eslint-disable-next-line es/no-object-hasown -- safe\nmodule.exports = Object.hasOwn || function hasOwn(it, key) {\n  return hasOwnProperty(toObject(it), key);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAoC,CAAC;AAC/D,IAAIC,QAAQ,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AAEhD,IAAIE,cAAc,GAAGH,WAAW,CAAC,CAAC,CAAC,CAACG,cAAc,CAAC;;AAEnD;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAGC,MAAM,CAACC,MAAM,IAAI,SAASA,MAAMA,CAACC,EAAE,EAAEC,GAAG,EAAE;EACzD,OAAON,cAAc,CAACD,QAAQ,CAACM,EAAE,CAAC,EAAEC,GAAG,CAAC;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}