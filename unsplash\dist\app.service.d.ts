import { ConfigService } from '@nestjs/config';
export declare class AppService {
    private configService;
    private readonly unsplashApiUrl;
    private readonly accessKey;
    constructor(configService: ConfigService);
    getHello(): string;
    getPhotos(page?: number, perPage?: number): Promise<any>;
    searchPhotos(query: string, page?: number, perPage?: number): Promise<any>;
    getPhotoById(id: string): Promise<any>;
}
